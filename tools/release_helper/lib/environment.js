const fs = require('fs-extra');
const path = require('path');

class EnvironmentManager {
  constructor(logger = null) {
    this.logger = logger;
    this.projectRoot = path.resolve(__dirname, '../../..');
    this.envSourceDir = path.join(this.projectRoot, 'app', 'env');
    this.envTargetFile = path.join(this.projectRoot, 'app', 'env', 'Environment.gd');
    this.gitignoreFile = path.join(this.projectRoot, '.gitignore');
  }

  async switchEnvironment(env) {
    this.logger?.step(`Switching to ${env} environment`);

    // Validate environment parameter
    if (!['prod', 'demo'].includes(env)) {
      throw new Error(`Invalid environment: ${env}. Must be 'prod' or 'demo'.`);
    }

    // Determine source file based on environment
    const sourceFileName = env === 'prod' 
      ? 'Environment.prod.steam.gd' 
      : 'Environment.prod.demo.steam.gd';
    
    const sourceFile = path.join(this.envSourceDir, sourceFileName);

    // Validate source environment file exists
    await this.validateEnvironmentFile(sourceFile);

    // Ensure .gitignore is properly configured
    await this.ensureGitIgnore();

    // Copy environment file
    await this.copyEnvironmentFile(sourceFile, this.envTargetFile);

    this.logger?.success(`Environment switched to ${env}`);
    this.logger?.debug(`Copied ${sourceFileName} to Environment.gd`);
  }

  async validateEnvironmentFile(sourceFile) {
    this.logger?.debug(`Validating environment file: ${sourceFile}`);

    if (!await fs.pathExists(sourceFile)) {
      throw new Error(`Environment file not found: ${sourceFile}`);
    }

    // Check if file is readable
    try {
      await fs.access(sourceFile, fs.constants.R_OK);
    } catch (error) {
      throw new Error(`Environment file is not readable: ${sourceFile}`);
    }

    // Basic validation of file content
    const content = await fs.readFile(sourceFile, 'utf8');
    
    // Check for required Godot script structure
    if (!content.includes('extends Node')) {
      throw new Error(`Invalid environment file format: ${sourceFile}. Must be a Godot script extending Node.`);
    }

    // Check for required constants
    const requiredConstants = ['EnvironmentName', 'StoreType', 'SteamAppId'];
    for (const constant of requiredConstants) {
      if (!content.includes(`const ${constant}`)) {
        this.logger?.warn(`Environment file ${sourceFile} missing recommended constant: ${constant}`);
      }
    }

    this.logger?.debug('Environment file validation completed');
  }

  async copyEnvironmentFile(sourceFile, targetFile) {
    this.logger?.debug(`Copying ${sourceFile} to ${targetFile}`);

    try {
      // Ensure target directory exists
      await fs.ensureDir(path.dirname(targetFile));

      // Copy the file
      await fs.copy(sourceFile, targetFile, { overwrite: true });

      // Verify the copy was successful
      if (!await fs.pathExists(targetFile)) {
        throw new Error('Failed to copy environment file');
      }

      this.logger?.debug('Environment file copied successfully');
    } catch (error) {
      throw new Error(`Failed to copy environment file: ${error.message}`);
    }
  }

  async ensureGitIgnore() {
    this.logger?.debug('Ensuring .gitignore is properly configured');

    const gitignoreEntry = 'app/env/Environment.gd';
    
    try {
      let gitignoreContent = '';
      
      // Read existing .gitignore if it exists
      if (await fs.pathExists(this.gitignoreFile)) {
        gitignoreContent = await fs.readFile(this.gitignoreFile, 'utf8');
      }

      // Check if the entry already exists
      if (gitignoreContent.includes(gitignoreEntry)) {
        this.logger?.debug('.gitignore already contains Environment.gd entry');
        return;
      }

      // Add the entry to .gitignore
      const newEntry = gitignoreContent.endsWith('\n') ? gitignoreEntry : `\n${gitignoreEntry}`;
      gitignoreContent += newEntry + '\n';

      await fs.writeFile(this.gitignoreFile, gitignoreContent);
      this.logger?.debug('Added Environment.gd to .gitignore');

    } catch (error) {
      this.logger?.warn(`Failed to update .gitignore: ${error.message}`);
      // Don't throw error as this is not critical for deployment
    }
  }

  async getCurrentEnvironment() {
    this.logger?.debug('Detecting current environment');

    if (!await fs.pathExists(this.envTargetFile)) {
      this.logger?.debug('No Environment.gd file found');
      return null;
    }

    try {
      const content = await fs.readFile(this.envTargetFile, 'utf8');
      
      // Try to detect environment from content
      if (content.includes('const IsDemo = true')) {
        return 'demo';
      } else if (content.includes('const IsDemo = false')) {
        // Check if it's production by looking for production app ID
        if (content.includes('SteamAppId  = "3391210"')) {
          return 'prod';
        }
      }

      this.logger?.debug('Could not determine environment from Environment.gd content');
      return 'unknown';
    } catch (error) {
      this.logger?.warn(`Failed to read current environment: ${error.message}`);
      return null;
    }
  }

  async listAvailableEnvironments() {
    this.logger?.debug('Listing available environment files');

    try {
      const files = await fs.readdir(this.envSourceDir);
      const envFiles = files.filter(file => 
        file.startsWith('Environment.') && 
        file.endsWith('.gd') && 
        file !== 'Environment.gd'
      );

      const environments = [];
      for (const file of envFiles) {
        if (file.includes('prod.steam.gd') && !file.includes('demo')) {
          environments.push({ env: 'prod', file });
        } else if (file.includes('prod.demo.steam.gd')) {
          environments.push({ env: 'demo', file });
        }
      }

      this.logger?.debug(`Found ${environments.length} environment files`);
      return environments;
    } catch (error) {
      throw new Error(`Failed to list environment files: ${error.message}`);
    }
  }

  // Backup current environment before switching
  async backupCurrentEnvironment() {
    if (!await fs.pathExists(this.envTargetFile)) {
      this.logger?.debug('No current environment to backup');
      return null;
    }

    const backupFile = path.join(this.envSourceDir, `Environment.backup.${Date.now()}.gd`);
    
    try {
      await fs.copy(this.envTargetFile, backupFile);
      this.logger?.debug(`Current environment backed up to: ${backupFile}`);
      return backupFile;
    } catch (error) {
      this.logger?.warn(`Failed to backup current environment: ${error.message}`);
      return null;
    }
  }
}

module.exports = EnvironmentManager;
