const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');

class BuildManager {
  constructor(logger = null) {
    this.logger = logger;
    this.projectRoot = path.resolve(__dirname, '../../..');
    this.appDir = path.join(this.projectRoot, 'app');
    this.releaseDir = path.join(this.projectRoot, '_release');
    
    // Platform configurations
    this.platforms = {
      windows: {
        name: 'Windows',
        preset: 'Windows Desktop',
        outputDir: path.join(this.releaseDir, 'win', 'current'),
        extension: '.exe'
      },
      linux: {
        name: 'Linux',
        preset: 'Linux/X11',
        outputDir: path.join(this.releaseDir, 'linux', 'current'),
        extension: '.x86_64'
      },
      mac: {
        name: 'macOS',
        preset: 'Mac OSX',
        outputDir: path.join(this.releaseDir, 'mac', 'current'),
        extension: '.zip'
      }
    };
  }

  async buildAll() {
    this.logger?.step('Building for all platforms');
    
    const platforms = ['windows', 'linux', 'mac'];
    const results = {};

    for (const platform of platforms) {
      try {
        results[platform] = await this.buildPlatform(platform);
      } catch (error) {
        this.logger?.error(`Failed to build ${platform}: ${error.message}`);
        results[platform] = { success: false, error: error.message };
      }
    }

    // Summary
    const successful = Object.values(results).filter(r => r.success).length;
    const total = platforms.length;
    
    if (successful === total) {
      this.logger?.success(`All ${total} platform builds completed successfully`);
    } else {
      this.logger?.warn(`${successful}/${total} platform builds completed successfully`);
    }

    return results;
  }

  async buildPlatform(platform) {
    if (!this.platforms[platform]) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    const config = this.platforms[platform];
    this.logger?.step(`Building for ${config.name}`);

    // Clean build directory
    await this.cleanBuildDirectory(platform);

    // Execute Godot export
    const result = await this.executeGodotExport(platform);

    // Platform-specific post-processing (must happen before verification)
    await this.postProcessBuild(platform);

    // Verify build output
    await this.verifyBuildOutput(platform);

    this.logger?.success(`${config.name} build completed`);
    return { success: true, platform, outputDir: config.outputDir };
  }

  async cleanBuildDirectory(platform) {
    const config = this.platforms[platform];
    this.logger?.debug(`Cleaning build directory: ${config.outputDir}`);

    try {
      // Ensure the directory exists
      await fs.ensureDir(config.outputDir);

      // Remove all contents
      const items = await fs.readdir(config.outputDir);
      for (const item of items) {
        const itemPath = path.join(config.outputDir, item);
        await fs.remove(itemPath);
      }

      this.logger?.debug(`Build directory cleaned: ${config.outputDir}`);
    } catch (error) {
      throw new Error(`Failed to clean build directory: ${error.message}`);
    }
  }

  async executeGodotExport(platform) {
    const config = this.platforms[platform];
    const outputPath = path.join(config.outputDir, `tryu${config.extension}`);

    this.logger?.debug(`Executing Godot export for ${config.name}`);
    this.logger?.debug(`Preset: ${config.preset}`);
    this.logger?.debug(`Output: ${outputPath}`);

    return new Promise((resolve, reject) => {
      // Try different Godot executable names
      const godotCommands = ['godot3-bin', 'godot', 'godot-headless', 'godot.exe'];
      let currentCommandIndex = 0;

      const tryNextCommand = () => {
        if (currentCommandIndex >= godotCommands.length) {
          reject(new Error('Godot executable not found. Please ensure Godot is installed and available in PATH.'));
          return;
        }

        const godotCmd = godotCommands[currentCommandIndex];
        currentCommandIndex++;

        const args = [
          '--headless',
          '--export-debug',
          config.preset,
          outputPath
        ];

        this.logger?.debug(`Trying command: ${godotCmd} ${args.join(' ')}`);
        this.logger?.debug(`Working directory: ${this.appDir}`);
        this.logger?.debug(`Output path: ${outputPath}`);

        const godotProcess = spawn(godotCmd, args, {
          cwd: this.appDir,
          stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        godotProcess.stdout.on('data', (data) => {
          stdout += data.toString();
        });

        godotProcess.stderr.on('data', (data) => {
          stderr += data.toString();
        });

        godotProcess.on('error', (error) => {
          if (error.code === 'ENOENT') {
            // Command not found, try next one
            tryNextCommand();
          } else {
            reject(new Error(`Godot process error: ${error.message}`));
          }
        });

        godotProcess.on('close', (code) => {
          this.logger?.debug(`Godot process exited with code: ${code}`);
          this.logger?.debug(`Stdout: ${stdout}`);
          this.logger?.debug(`Stderr: ${stderr}`);

          if (code === 0) {
            this.logger?.debug(`Godot export completed successfully`);
            resolve({ code, stdout, stderr });
          } else {
            // Provide helpful error messages for common issues
            let errorMessage = `Godot export failed with code ${code}`;
            const output = stderr || stdout;

            if (output.includes('No export template found')) {
              errorMessage += '\n\nMissing export templates. Please install export templates in Godot editor:\n' +
                           '1. Open Godot editor\n' +
                           '2. Go to Editor -> Manage Export Templates\n' +
                           '3. Download templates for your Godot version';
            } else if (output.includes('Parse Error') || output.includes('SCRIPT ERROR')) {
              errorMessage += '\n\nScript errors in project. Please fix script errors in Godot editor before building.';
            } else if (output.includes('--export option was changed')) {
              errorMessage += '\n\nGodot version mismatch. This appears to be a Godot 3 project but Godot 4 is installed.';
            }

            errorMessage += `\n\nFull output:\n${output}`;
            reject(new Error(errorMessage));
          }
        });
      };

      tryNextCommand();
    });
  }

  async verifyBuildOutput(platform) {
    const config = this.platforms[platform];

    // For macOS, after post-processing we expect a .app bundle, not a .zip
    let expectedOutput;
    if (platform === 'mac') {
      // Check for both possible .app bundle names
      expectedOutput = path.join(config.outputDir, 'tryu.app');
      if (!await fs.pathExists(expectedOutput)) {
        expectedOutput = path.join(config.outputDir, 'Tryu.app');
      }
    } else {
      expectedOutput = path.join(config.outputDir, `tryu${config.extension}`);
    }

    this.logger?.debug(`Verifying build output: ${expectedOutput}`);

    if (!await fs.pathExists(expectedOutput)) {
      throw new Error(`Build output not found: ${expectedOutput}`);
    }

    const stats = await fs.stat(expectedOutput);

    // For directories (like .app bundles), check if it's a valid directory
    if (stats.isDirectory()) {
      // For .app bundles, check if it contains the expected structure
      if (platform === 'mac') {
        const contentsDir = path.join(expectedOutput, 'Contents');
        if (!await fs.pathExists(contentsDir)) {
          throw new Error(`Invalid .app bundle: missing Contents directory in ${expectedOutput}`);
        }
        this.logger?.debug(`Build output verified: ${expectedOutput} (.app bundle)`);
      } else {
        this.logger?.debug(`Build output verified: ${expectedOutput} (directory)`);
      }
    } else {
      // For files, check size
      if (stats.size === 0) {
        throw new Error(`Build output is empty: ${expectedOutput}`);
      }
      this.logger?.debug(`Build output verified: ${expectedOutput} (${stats.size} bytes)`);
    }
  }

  async postProcessBuild(platform) {
    const config = this.platforms[platform];

    switch (platform) {
      case 'mac':
        await this.postProcessMacBuild(config);
        break;
      case 'linux':
        await this.postProcessLinuxBuild(config);
        break;
      case 'windows':
        await this.postProcessWindowsBuild(config);
        break;
    }
  }

  async postProcessMacBuild(config) {
    this.logger?.debug('Post-processing macOS build');

    const zipPath = path.join(config.outputDir, 'tryu.zip');
    const extractDir = config.outputDir;

    if (await fs.pathExists(zipPath)) {
      this.logger?.debug('Extracting macOS .app bundle from zip');

      try {
        // Use unzip command to extract the zip file
        const { spawn } = require('child_process');

        await new Promise((resolve, reject) => {
          const unzipProcess = spawn('unzip', ['-o', zipPath, '-d', extractDir], {
            stdio: 'pipe'
          });

          unzipProcess.on('close', (code) => {
            if (code === 0) {
              resolve();
            } else {
              reject(new Error(`Unzip failed with code ${code}`));
            }
          });

          unzipProcess.on('error', reject);
        });

        // Remove the zip file after successful extraction
        await fs.remove(zipPath);
        this.logger?.debug('Extracted .app bundle and removed zip file');

        // Verify the .app bundle exists (check both possible names)
        let appBundlePath = path.join(extractDir, 'tryu.app');
        if (!await fs.pathExists(appBundlePath)) {
          appBundlePath = path.join(extractDir, 'Tryu.app');
        }

        if (await fs.pathExists(appBundlePath)) {
          this.logger?.debug(`macOS .app bundle ready for Steam upload: ${path.basename(appBundlePath)}`);
        } else {
          throw new Error('Expected .app bundle not found after extraction');
        }

      } catch (error) {
        throw new Error(`Failed to process macOS build: ${error.message}`);
      }
    }
  }

  async postProcessLinuxBuild(config) {
    this.logger?.debug('Post-processing Linux build');
    
    const executablePath = path.join(config.outputDir, 'tryu');
    
    if (await fs.pathExists(executablePath)) {
      // Make executable
      await fs.chmod(executablePath, 0o755);
      this.logger?.debug('Set executable permissions for Linux build');
    }
  }

  async postProcessWindowsBuild(config) {
    this.logger?.debug('Post-processing Windows build');

    // Handle case where Godot creates .tmp file instead of .exe (missing rcedit)
    const expectedPath = path.join(config.outputDir, 'tryu.exe');
    const tmpPath = path.join(config.outputDir, 'tryu.tmp');

    if (!await fs.pathExists(expectedPath) && await fs.pathExists(tmpPath)) {
      this.logger?.debug('Found .tmp file, renaming to .exe (rcedit not available)');
      await fs.move(tmpPath, expectedPath);
    }
  }

  async getBuildInfo(platform) {
    if (!this.platforms[platform]) {
      throw new Error(`Unsupported platform: ${platform}`);
    }

    const config = this.platforms[platform];
    const outputPath = path.join(config.outputDir, `tryu${config.extension}`);

    if (!await fs.pathExists(outputPath)) {
      return { exists: false, platform, outputPath };
    }

    const stats = await fs.stat(outputPath);
    return {
      exists: true,
      platform,
      outputPath,
      size: stats.size,
      modified: stats.mtime
    };
  }

  async listBuilds() {
    const builds = {};
    
    for (const [platform, config] of Object.entries(this.platforms)) {
      builds[platform] = await this.getBuildInfo(platform);
    }

    return builds;
  }

  // Check if Godot is available
  async checkGodotAvailability() {
    const godotCommands = ['godot3-bin', 'godot', 'godot-headless', 'godot.exe'];
    
    for (const cmd of godotCommands) {
      try {
        await new Promise((resolve, reject) => {
          const process = spawn(cmd, ['--version'], { stdio: 'pipe' });
          process.on('error', reject);
          process.on('close', (code) => {
            if (code === 0) resolve();
            else reject(new Error(`Exit code: ${code}`));
          });
        });
        
        this.logger?.debug(`Found Godot executable: ${cmd}`);
        return cmd;
      } catch (error) {
        // Continue to next command
      }
    }
    
    throw new Error('Godot executable not found in PATH');
  }
}

module.exports = BuildManager;
