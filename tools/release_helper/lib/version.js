const fs = require('fs-extra');
const path = require('path');

class VersionManager {
  constructor(configPath = null, logger = null) {
    this.configPath = configPath || path.resolve(__dirname, '../../../app/scripts/Config.gd');
    this.logger = logger;
    this.currentVersion = null;
  }

  async getCurrentVersion() {
    this.logger?.debug(`Reading version from: ${this.configPath}`);

    if (!await fs.pathExists(this.configPath)) {
      throw new Error(`Config file not found: ${this.configPath}`);
    }

    try {
      const content = await fs.readFile(this.configPath, 'utf8');
      
      // Look for version constant using regex
      const versionMatch = content.match(/const\s+version\s*=\s*"([^"]+)"/);
      
      if (!versionMatch) {
        throw new Error('Version constant not found in Config.gd. Expected format: const version = "x.y.z"');
      }

      const version = versionMatch[1];
      this.logger?.debug(`Found version: ${version}`);

      // Validate SemVer format
      if (!this.isValidSemVer(version)) {
        throw new Error(`Invalid version format: ${version}. Expected SemVer format (x.y.z)`);
      }

      this.currentVersion = version;
      return version;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error(`Config file not found: ${this.configPath}`);
      }
      throw error;
    }
  }

  isValidSemVer(version) {
    const semverRegex = /^\d+\.\d+\.\d+$/;
    return semverRegex.test(version);
  }

  parseSemVer(version) {
    const parts = version.split('.');
    return {
      major: parseInt(parts[0], 10),
      minor: parseInt(parts[1], 10),
      patch: parseInt(parts[2], 10)
    };
  }

  formatSemVer(major, minor, patch) {
    return `${major}.${minor}.${patch}`;
  }

  async bumpVersion(type) {
    this.logger?.step(`Bumping ${type} version`);

    // Validate bump type
    if (!['major', 'minor', 'patch'].includes(type)) {
      throw new Error(`Invalid version bump type: ${type}. Must be 'major', 'minor', or 'patch'.`);
    }

    // Get current version if not already loaded
    if (!this.currentVersion) {
      await this.getCurrentVersion();
    }

    // Parse current version
    const { major, minor, patch } = this.parseSemVer(this.currentVersion);
    this.logger?.debug(`Current version parts: major=${major}, minor=${minor}, patch=${patch}`);

    // Calculate new version
    let newMajor = major;
    let newMinor = minor;
    let newPatch = patch;

    switch (type) {
      case 'major':
        newMajor += 1;
        newMinor = 0;
        newPatch = 0;
        break;
      case 'minor':
        newMinor += 1;
        newPatch = 0;
        break;
      case 'patch':
        newPatch += 1;
        break;
    }

    const newVersion = this.formatSemVer(newMajor, newMinor, newPatch);
    this.logger?.debug(`New version: ${newVersion}`);

    // Store old version for logging
    const oldVersion = this.currentVersion;

    // Update the config file
    await this.updateConfigFile(newVersion);

    this.currentVersion = newVersion;
    this.logger?.success(`Version bumped from ${oldVersion} to ${newVersion}`);
    
    return newVersion;
  }

  async updateConfigFile(newVersion) {
    this.logger?.debug(`Updating config file with version: ${newVersion}`);

    try {
      // Read current content
      const content = await fs.readFile(this.configPath, 'utf8');

      // Replace version constant
      const updatedContent = content.replace(
        /const\s+version\s*=\s*"[^"]+"/,
        `const version = "${newVersion}"`
      );

      // Verify the replacement was made
      if (content === updatedContent) {
        throw new Error('Failed to update version in config file. Version constant not found or already up to date.');
      }

      // Create backup of original file
      const backupPath = `${this.configPath}.backup.${Date.now()}`;
      await fs.copy(this.configPath, backupPath);
      this.logger?.debug(`Created backup: ${backupPath}`);

      // Write updated content
      await fs.writeFile(this.configPath, updatedContent, 'utf8');

      // Verify the update
      const verifyContent = await fs.readFile(this.configPath, 'utf8');
      if (!verifyContent.includes(`const version = "${newVersion}"`)) {
        throw new Error('Failed to verify version update in config file');
      }

      this.logger?.debug('Config file updated successfully');
    } catch (error) {
      throw new Error(`Failed to update config file: ${error.message}`);
    }
  }

  formatVersionForPlatform(version, platform) {
    if (!version) {
      version = this.currentVersion;
    }

    if (!version) {
      throw new Error('No version available. Call getCurrentVersion() or bumpVersion() first.');
    }

    const { major, minor, patch } = this.parseSemVer(version);

    switch (platform) {
      case 'windows':
      case 'mac':
        // Windows and macOS use 4-number versioning: major.minor.patch.0
        return `${major}.${minor}.${patch}.0`;
      case 'linux':
        // Linux typically uses 3-number versioning
        return `${major}.${minor}.${patch}`;
      default:
        // Default to 3-number versioning
        return `${major}.${minor}.${patch}`;
    }
  }

  async getVersionInfo() {
    if (!this.currentVersion) {
      await this.getCurrentVersion();
    }

    const { major, minor, patch } = this.parseSemVer(this.currentVersion);

    return {
      current: this.currentVersion,
      major,
      minor,
      patch,
      platforms: {
        windows: this.formatVersionForPlatform(this.currentVersion, 'windows'),
        linux: this.formatVersionForPlatform(this.currentVersion, 'linux'),
        mac: this.formatVersionForPlatform(this.currentVersion, 'mac')
      }
    };
  }

  // Preview what the next version would be without actually bumping
  previewBump(type) {
    if (!this.currentVersion) {
      throw new Error('No current version loaded. Call getCurrentVersion() first.');
    }

    const { major, minor, patch } = this.parseSemVer(this.currentVersion);

    let newMajor = major;
    let newMinor = minor;
    let newPatch = patch;

    switch (type) {
      case 'major':
        newMajor += 1;
        newMinor = 0;
        newPatch = 0;
        break;
      case 'minor':
        newMinor += 1;
        newPatch = 0;
        break;
      case 'patch':
        newPatch += 1;
        break;
      default:
        throw new Error(`Invalid version bump type: ${type}`);
    }

    return this.formatSemVer(newMajor, newMinor, newPatch);
  }
}

module.exports = VersionManager;
