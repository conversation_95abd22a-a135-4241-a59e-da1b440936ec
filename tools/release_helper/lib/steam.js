const fs = require('fs-extra');
const path = require('path');
const { spawn } = require('child_process');

class SteamUploader {
  constructor(config, logger = null) {
    this.config = config;
    this.logger = logger;
    this.projectRoot = path.resolve(__dirname, '../../..');
    this.templatesDir = path.join(__dirname, '../templates');
    this.releaseDir = path.join(this.projectRoot, '_release');
    this.steamLogsDir = path.join(this.releaseDir, 'steam_logs');
  }

  async upload(environment, contentDesc = '') {
    this.logger?.step(`Uploading to Steam (${environment} environment)`);

    // Get environment configuration
    const envConfig = this.config.getEnvironmentConfig(environment);
    
    // Ensure steam logs directory exists
    await fs.ensureDir(this.steamLogsDir);

    // Generate VDF file
    const vdfPath = await this.generateVDF(envConfig.appId, envConfig.depots, contentDesc);

    // Check SteamCMD availability
    await this.checkSteamCMDAvailability();

    // Execute SteamCMD upload
    const result = await this.executeSteamCMD(vdfPath);

    // Validate upload success
    await this.validateUpload(result);

    this.logger?.success(`Steam upload completed for ${environment} environment`);
    return result;
  }

  async generateVDF(appId, depots, contentDesc) {
    this.logger?.debug('Generating Steam VDF file');

    const templatePath = path.join(this.templatesDir, 'app_build.vdf');
    const outputPath = path.join(this.steamLogsDir, `app_build_${appId}.vdf`);

    // Read template
    if (!await fs.pathExists(templatePath)) {
      throw new Error(`VDF template not found: ${templatePath}`);
    }

    let vdfContent = await fs.readFile(templatePath, 'utf8');

    // Replace placeholders
    vdfContent = vdfContent
      .replace(/{{APP_ID}}/g, appId.toString())
      .replace(/{{CONTENT_DESC}}/g, contentDesc || `Automated build ${new Date().toISOString()}`)
      .replace(/{{WINDOWS_DEPOT_ID}}/g, depots.windows.toString())
      .replace(/{{LINUX_DEPOT_ID}}/g, depots.linux.toString())
      .replace(/{{MAC_DEPOT_ID}}/g, depots.mac.toString());

    // Write generated VDF
    await fs.writeFile(outputPath, vdfContent, 'utf8');

    this.logger?.debug(`VDF file generated: ${outputPath}`);
    this.logger?.debug(`App ID: ${appId}`);
    this.logger?.debug(`Depots: Windows=${depots.windows}, Linux=${depots.linux}, Mac=${depots.mac}`);

    return outputPath;
  }

  async executeSteamCMD(vdfPath) {
    this.logger?.debug(`Executing SteamCMD with VDF: ${vdfPath}`);

    return new Promise((resolve, reject) => {
      // Try different SteamCMD executable names
      const steamcmdCommands = ['steamcmd', 'steamcmd.exe', './steamcmd.sh'];
      let currentCommandIndex = 0;

      const tryNextCommand = () => {
        if (currentCommandIndex >= steamcmdCommands.length) {
          reject(new Error('SteamCMD executable not found. Please ensure SteamCMD is installed and available in PATH.'));
          return;
        }

        const steamcmdCmd = steamcmdCommands[currentCommandIndex];
        currentCommandIndex++;

        const args = [
          '+login', process.env.STEAM_USERNAME || 'anonymous',
          '+run_app_build', vdfPath,
          '+quit'
        ];

        // If Steam credentials are provided via environment variables
        if (process.env.STEAM_PASSWORD) {
          args[1] = process.env.STEAM_USERNAME;
          args.splice(2, 0, process.env.STEAM_PASSWORD);
        }

        this.logger?.debug(`Trying SteamCMD command: ${steamcmdCmd} ${args.join(' ')}`);
        this.logger?.info('Starting Steam upload... This may take several minutes.');

        const steamProcess = spawn(steamcmdCmd, args, {
          cwd: this.releaseDir,
          stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        steamProcess.stdout.on('data', (data) => {
          const output = data.toString();
          stdout += output;
          
          // Log progress indicators
          if (output.includes('Uploading') || output.includes('Progress:')) {
            this.logger?.info(output.trim());
          }
        });

        steamProcess.stderr.on('data', (data) => {
          stderr += data.toString();
        });

        steamProcess.on('error', (error) => {
          if (error.code === 'ENOENT') {
            // Command not found, try next one
            tryNextCommand();
          } else {
            reject(new Error(`SteamCMD process error: ${error.message}`));
          }
        });

        steamProcess.on('close', (code) => {
          if (code === 0) {
            this.logger?.debug('SteamCMD completed successfully');
            resolve({ code, stdout, stderr });
          } else {
            this.logger?.debug(`SteamCMD failed with code: ${code}`);
            this.logger?.debug(`Stdout: ${stdout}`);
            this.logger?.debug(`Stderr: ${stderr}`);
            
            let errorMessage = `SteamCMD failed with code ${code}`;
            const output = stderr || stdout;
            
            if (output.includes('Invalid Password') || output.includes('Invalid Username')) {
              errorMessage += '\n\nSteam authentication failed. Please check your Steam credentials.\n' +
                           'Set environment variables: STEAM_USERNAME and STEAM_PASSWORD';
            } else if (output.includes('Two-factor')) {
              errorMessage += '\n\nTwo-factor authentication required. Please use Steam Guard Mobile Authenticator.';
            } else if (output.includes('App ID') && output.includes('not found')) {
              errorMessage += '\n\nInvalid Steam App ID. Please check your deploy.config.json configuration.';
            }
            
            errorMessage += `\n\nFull output:\n${output}`;
            reject(new Error(errorMessage));
          }
        });
      };

      tryNextCommand();
    });
  }

  async validateUpload(result) {
    this.logger?.debug('Validating Steam upload');

    const { stdout } = result;
    
    // Check for success indicators in SteamCMD output
    if (stdout.includes('Success') || stdout.includes('App build complete')) {
      this.logger?.debug('Upload validation successful');
      return true;
    }

    // Check for specific error patterns
    if (stdout.includes('Failed') || stdout.includes('Error')) {
      throw new Error('Steam upload validation failed - check SteamCMD output for details');
    }

    this.logger?.warn('Could not definitively validate upload success - check Steam partner site');
    return false;
  }

  async checkSteamCMDAvailability() {
    const steamcmdCommands = ['steamcmd', 'steamcmd.exe', './steamcmd.sh'];
    
    for (const cmd of steamcmdCommands) {
      try {
        await new Promise((resolve, reject) => {
          const process = spawn(cmd, ['+quit'], { stdio: 'pipe' });
          process.on('error', reject);
          process.on('close', (code) => {
            // SteamCMD returns 0 even with +quit, so any exit is considered success for availability check
            resolve();
          });
        });
        
        this.logger?.debug(`Found SteamCMD executable: ${cmd}`);
        return cmd;
      } catch (error) {
        // Continue to next command
      }
    }
    
    throw new Error('SteamCMD not found. Please install SteamCMD and ensure it\'s available in PATH.');
  }

  // Get upload status for specific depots
  async getUploadStatus(environment) {
    const envConfig = this.config.getEnvironmentConfig(environment);
    const logFiles = await this.findSteamLogFiles(envConfig.appId);
    
    if (logFiles.length === 0) {
      return { status: 'no_logs', message: 'No upload logs found' };
    }

    // Parse most recent log file for status
    const latestLog = logFiles[0]; // Assuming sorted by date
    const logContent = await fs.readFile(latestLog, 'utf8');
    
    if (logContent.includes('App build complete')) {
      return { status: 'success', message: 'Upload completed successfully' };
    } else if (logContent.includes('Failed') || logContent.includes('Error')) {
      return { status: 'failed', message: 'Upload failed - check logs' };
    } else {
      return { status: 'unknown', message: 'Upload status unclear' };
    }
  }

  async findSteamLogFiles(appId) {
    try {
      const files = await fs.readdir(this.steamLogsDir);
      const logFiles = files
        .filter(file => file.includes(appId.toString()) && file.endsWith('.log'))
        .map(file => path.join(this.steamLogsDir, file));
      
      // Sort by modification time (newest first)
      const stats = await Promise.all(logFiles.map(async file => ({
        file,
        mtime: (await fs.stat(file)).mtime
      })));
      
      return stats
        .sort((a, b) => b.mtime - a.mtime)
        .map(item => item.file);
    } catch (error) {
      this.logger?.debug(`Error reading steam logs directory: ${error.message}`);
      return [];
    }
  }

  // Clean up old VDF and log files
  async cleanup(keepDays = 7) {
    this.logger?.debug(`Cleaning up Steam files older than ${keepDays} days`);
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - keepDays);
    
    try {
      const files = await fs.readdir(this.steamLogsDir);
      let cleanedCount = 0;
      
      for (const file of files) {
        const filePath = path.join(this.steamLogsDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime < cutoffDate) {
          await fs.remove(filePath);
          cleanedCount++;
        }
      }
      
      this.logger?.debug(`Cleaned up ${cleanedCount} old Steam files`);
    } catch (error) {
      this.logger?.warn(`Failed to clean up Steam files: ${error.message}`);
    }
  }
}

module.exports = SteamUploader;
