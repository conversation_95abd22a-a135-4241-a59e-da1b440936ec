const fs = require('fs-extra');
const path = require('path');

class Logger {
  constructor(logPath = 'deploy.log') {
    this.logPath = path.resolve(logPath);
    this.logLevels = {
      DEBUG: 0,
      INFO: 1,
      WARN: 2,
      ERROR: 3
    };
    this.currentLevel = this.logLevels.INFO;
    
    // Ensure log directory exists
    fs.ensureDirSync(path.dirname(this.logPath));
    
    // Initialize log file with session header
    this.writeToFile(`\n=== Deployment Session Started: ${new Date().toISOString()} ===\n`);
  }

  setLevel(level) {
    if (typeof level === 'string') {
      this.currentLevel = this.logLevels[level.toUpperCase()] || this.logLevels.INFO;
    } else {
      this.currentLevel = level;
    }
  }

  formatMessage(level, message) {
    const timestamp = new Date().toISOString();
    return `[${timestamp}] [${level}] ${message}`;
  }

  writeToFile(message) {
    try {
      fs.appendFileSync(this.logPath, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  log(level, message, consoleOutput = true) {
    const levelValue = this.logLevels[level];
    if (levelValue < this.currentLevel) {
      return;
    }

    const formattedMessage = this.formatMessage(level, message);
    
    // Write to file
    this.writeToFile(formattedMessage);
    
    // Write to console if enabled
    if (consoleOutput) {
      switch (level) {
        case 'ERROR':
          console.error(`❌ ${message}`);
          break;
        case 'WARN':
          console.warn(`⚠️  ${message}`);
          break;
        case 'INFO':
          console.log(`ℹ️  ${message}`);
          break;
        case 'DEBUG':
          console.log(`🐛 ${message}`);
          break;
        default:
          console.log(message);
      }
    }
  }

  debug(message, consoleOutput = false) {
    this.log('DEBUG', message, consoleOutput);
  }

  info(message, consoleOutput = true) {
    this.log('INFO', message, consoleOutput);
  }

  warn(message, consoleOutput = true) {
    this.log('WARN', message, consoleOutput);
  }

  error(message, consoleOutput = true) {
    this.log('ERROR', message, consoleOutput);
  }

  success(message, consoleOutput = true) {
    const formattedMessage = this.formatMessage('INFO', `SUCCESS: ${message}`);
    this.writeToFile(formattedMessage);
    
    if (consoleOutput) {
      console.log(`✅ ${message}`);
    }
  }

  step(message, consoleOutput = true) {
    const formattedMessage = this.formatMessage('INFO', `STEP: ${message}`);
    this.writeToFile(formattedMessage);
    
    if (consoleOutput) {
      console.log(`🔄 ${message}`);
    }
  }

  separator(consoleOutput = true) {
    const separator = '─'.repeat(60);
    this.writeToFile(separator);
    
    if (consoleOutput) {
      console.log(separator);
    }
  }

  sessionEnd() {
    this.writeToFile(`=== Deployment Session Ended: ${new Date().toISOString()} ===\n`);
  }
}

module.exports = Logger;
