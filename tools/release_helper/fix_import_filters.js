#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');

/**
 * Fix <PERSON>ot import files to disable texture filtering for pixel art
 * This prevents sprites from becoming blurry during export
 */

async function fixImportFiles() {
  console.log('🔧 Fixing texture import files to disable filtering...');
  
  const projectRoot = path.resolve(__dirname, '../..');
  const appDir = path.join(projectRoot, 'app');
  
  // Find all .import files for PNG textures recursively
  const importFiles = [];

  function findImportFiles(dir) {
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        findImportFiles(fullPath);
      } else if (item.endsWith('.png.import')) {
        importFiles.push(fullPath);
      }
    }
  }

  findImportFiles(appDir);
  
  console.log(`📁 Found ${importFiles.length} texture import files`);
  
  let fixedCount = 0;
  let alreadyCorrectCount = 0;
  
  for (const importFile of importFiles) {
    try {
      const content = await fs.readFile(importFile, 'utf8');
      
      // Check if filter is currently enabled
      if (content.includes('flags/filter=true')) {
        // Replace filter=true with filter=false
        const fixedContent = content.replace(/flags\/filter=true/g, 'flags/filter=false');
        
        // Also ensure fix_alpha_border is false for pixel art (prevents blurring)
        const finalContent = fixedContent.replace(/process\/fix_alpha_border=true/g, 'process/fix_alpha_border=false');
        
        await fs.writeFile(importFile, finalContent, 'utf8');
        
        const relativePath = path.relative(appDir, importFile);
        console.log(`✅ Fixed: ${relativePath}`);
        fixedCount++;
      } else if (content.includes('flags/filter=false')) {
        alreadyCorrectCount++;
      } else {
        console.log(`⚠️  No filter flag found in: ${path.relative(appDir, importFile)}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${importFile}: ${error.message}`);
    }
  }
  
  console.log('\n📊 Summary:');
  console.log(`✅ Fixed files: ${fixedCount}`);
  console.log(`✓  Already correct: ${alreadyCorrectCount}`);
  console.log(`📁 Total processed: ${importFiles.length}`);
  
  if (fixedCount > 0) {
    console.log('\n🎯 Import files have been fixed! Sprites should now be crisp in builds.');
    console.log('💡 Note: You may need to rebuild to see the changes.');
  } else {
    console.log('\n✨ All import files were already correctly configured!');
  }
}

// Run the fix
if (require.main === module) {
  fixImportFiles().catch(error => {
    console.error('❌ Failed to fix import files:', error.message);
    process.exit(1);
  });
}

module.exports = { fixImportFiles };
