# Tryu Sora Space Shooter - Deployment Tools

Automated deployment tools for versioning, building, and uploading the Tryu Sora Space Shooter game to Steam.

## Features

- **Version Management**: Automatic SemVer version bumping in `app/scripts/Config.gd`
- **Environment Switching**: Switch between production and demo environments
- **Multi-Platform Building**: Build for Windows, Linux, and macOS using Godot CLI
- **Steam Integration**: Automated upload to Steam using SteamCMD
- **Comprehensive Logging**: Detailed logs saved to `deploy.log`
- **Dry Run Mode**: Preview changes without executing them

## Prerequisites

### Required Software

1. **Node.js** >= 12.x
2. **Godot Engine** (version 3.x for this project)
3. **SteamCMD** (for Steam uploads)

### Installation

1. Install Node.js dependencies:
   ```bash
   cd tools/release_helper
   npm install
   ```

2. Install Godot Engine and ensure it's available in PATH:
   ```bash
   # On Ubuntu/Debian
   sudo apt install godot3
   
   # Or download from https://godotengine.org/
   ```

3. Install SteamCMD:
   ```bash
   # On Ubuntu/Debian
   sudo apt install steamcmd
   
   # Or download from https://developer.valvesoftware.com/wiki/SteamCMD
   ```

### Configuration

1. **Export Templates**: Install Godot export templates in the Godot editor:
   - Open Godot editor
   - Go to Editor → Manage Export Templates
   - Download templates for your Godot version

2. **Steam Credentials**: Set environment variables for Steam authentication:
   ```bash
   export STEAM_USERNAME="your_steam_username"
   export STEAM_PASSWORD="your_steam_password"
   ```

## Usage

### Command Line Options

```bash
node tools/release_helper/deploy.js [options]
```

| Option | Description |
|--------|-------------|
| `--bump-version <type>` | Bump version segment (major, minor, patch) |
| `--checkout-env <env>` | Switch to environment (prod, demo) |
| `--build-all` | Build for all platforms (Windows, Linux, macOS) |
| `--build-windows` | Build for Windows only |
| `--build-linux` | Build for Linux only |
| `--build-mac` | Build for macOS only |
| `--upload` | Upload builds to Steam |
| `--content-desc <description>` | Content description for Steam upload |
| `--verbose` | Enable verbose logging |
| `--dry-run` | Show what would be done without executing |

### Examples

#### Version Bumping
```bash
# Bump patch version (0.8.4 → 0.8.5)
node tools/release_helper/deploy.js --bump-version patch

# Bump minor version (0.8.4 → 0.9.0)
node tools/release_helper/deploy.js --bump-version minor

# Bump major version (0.8.4 → 1.0.0)
node tools/release_helper/deploy.js --bump-version major
```

#### Environment Switching
```bash
# Switch to production environment
node tools/release_helper/deploy.js --checkout-env prod

# Switch to demo environment
node tools/release_helper/deploy.js --checkout-env demo
```

#### Building
```bash
# Build for all platforms
node tools/release_helper/deploy.js --build-all

# Build for specific platforms
node tools/release_helper/deploy.js --build-windows --build-linux

# Build single platform
node tools/release_helper/deploy.js --build-linux
```

#### Steam Upload
```bash
# Upload to Steam (requires environment to be set)
node tools/release_helper/deploy.js --checkout-env prod --upload

# Upload with content description
node tools/release_helper/deploy.js --checkout-env prod --upload --content-desc "Bug fixes and performance improvements"
```

#### Complete Deployment Workflow
```bash
# Full deployment: version bump → environment → build → upload
node tools/release_helper/deploy.js --bump-version patch --checkout-env prod --build-all --upload --content-desc "Version 0.8.5 release"

# Preview the workflow without executing
node tools/release_helper/deploy.js --dry-run --bump-version patch --checkout-env prod --build-all --upload --content-desc "Version 0.8.5 release"
```

## Configuration Files

### deploy.config.json

Located in the project root, contains Steam app IDs and depot IDs:

```json
{
  "prod": {
    "appId": 3391210,
    "depots": {
      "windows": 3391212,
      "linux": 3391211,
      "mac": 3391213
    }
  },
  "demo": {
    "appId": 3612920,
    "depots": {
      "windows": 3612922,
      "linux": 3612921,
      "mac": 3612923
    }
  }
}
```

### Environment Files

Environment-specific configurations are stored in `app/env/`:
- `Environment.prod.steam.gd` - Production environment
- `Environment.prod.demo.steam.gd` - Demo environment

The script copies the appropriate file to `app/env/Environment.gd` (which is gitignored).

## Build Output

Builds are stored in the `_release` directory:
```
_release/
├── windows/current/    # Windows builds
├── linux/current/      # Linux builds
├── mac/current/        # macOS builds
└── steam_logs/         # Steam upload logs and VDF files
```

## Logging

All operations are logged to `tools/deploy.log` with timestamps and detailed information. Use `--verbose` flag for additional debug output.

## Troubleshooting

### Common Issues

1. **"Godot executable not found"**
   - Install Godot and ensure it's in your PATH
   - For Godot 3 projects, use Godot 3.x (not Godot 4)

2. **"No export template found"**
   - Install export templates in Godot editor
   - Go to Editor → Manage Export Templates

3. **"SteamCMD not found"**
   - Install SteamCMD and ensure it's in your PATH
   - Download from https://developer.valvesoftware.com/wiki/SteamCMD

4. **"Steam authentication failed"**
   - Set STEAM_USERNAME and STEAM_PASSWORD environment variables
   - Ensure Steam Guard is properly configured

5. **"Script errors in project"**
   - Fix GDScript errors in Godot editor before building
   - Check the full error output in the logs

### Debug Mode

Use `--verbose` flag to see detailed debug information:
```bash
node tools/release_helper/deploy.js --verbose --dry-run --build-all
```

## Development

### Project Structure

```
tools/release_helper/
├── deploy.js           # Main deployment script
├── lib/
│   ├── config.js       # Configuration management
│   ├── version.js      # Version bumping logic
│   ├── environment.js  # Environment switching
│   ├── build.js        # Build orchestration
│   ├── steam.js        # Steam upload logic
│   └── logger.js       # Logging utilities
├── templates/
│   └── app_build.vdf   # Steam VDF template
├── package.json        # Node.js dependencies
├── deploy.log          # Generated log file
└── README.md           # This file
```

### Adding New Features

1. Create new modules in `lib/` directory
2. Import and initialize in `deploy.js`
3. Add command-line options using Commander.js
4. Update this README with new usage examples

## License

This deployment tool is part of the Tryu Sora Space Shooter project.
