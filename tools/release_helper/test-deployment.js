#!/usr/bin/env node

/**
 * Test script for the deployment tool
 * Validates all components work correctly
 */

const { spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

class DeploymentTester {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '../..');
    this.passed = 0;
    this.failed = 0;
  }

  async runTest(name, testFn) {
    process.stdout.write(`Testing ${name}... `);
    try {
      await testFn();
      console.log('✅ PASSED');
      this.passed++;
    } catch (error) {
      console.log('❌ FAILED');
      console.log(`  Error: ${error.message}`);
      this.failed++;
    }
  }

  async runCommand(command, args = []) {
    return new Promise((resolve, reject) => {
      const process = spawn('node', [command, ...args], {
        cwd: this.projectRoot,
        stdio: 'pipe'
      });

      let stdout = '';
      let stderr = '';

      process.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      process.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr || stdout}`));
        }
      });
    });
  }

  async testDryRun() {
    const result = await this.runCommand('tools/release_helper/deploy.js', [
      '--dry-run',
      '--bump-version', 'patch',
      '--checkout-env', 'prod',
      '--build-all',
      '--upload',
      '--content-desc', 'Test deployment'
    ]);

    if (!result.stdout.includes('DRY RUN MODE')) {
      throw new Error('Dry run mode not detected');
    }

    if (!result.stdout.includes('Would bump version')) {
      throw new Error('Version bump not detected in dry run');
    }

    if (!result.stdout.includes('Would switch environment')) {
      throw new Error('Environment switch not detected in dry run');
    }

    if (!result.stdout.includes('Would build project')) {
      throw new Error('Build not detected in dry run');
    }

    if (!result.stdout.includes('Would upload to Steam')) {
      throw new Error('Upload not detected in dry run');
    }
  }

  async testVersionBumping() {
    // Get current version
    const configPath = path.join(this.projectRoot, 'app/scripts/Config.gd');
    const configContent = await fs.readFile(configPath, 'utf8');
    const versionMatch = configContent.match(/const version = "([^"]+)"/);
    
    if (!versionMatch) {
      throw new Error('Could not find version in Config.gd');
    }

    const currentVersion = versionMatch[1];
    const versionParts = currentVersion.split('.');
    const expectedNewVersion = `${versionParts[0]}.${versionParts[1]}.${parseInt(versionParts[2]) + 1}`;

    // Bump version
    await this.runCommand('tools/release_helper/deploy.js', ['--bump-version', 'patch']);

    // Verify new version
    const newConfigContent = await fs.readFile(configPath, 'utf8');
    const newVersionMatch = newConfigContent.match(/const version = "([^"]+)"/);
    
    if (!newVersionMatch) {
      throw new Error('Could not find version in Config.gd after bump');
    }

    const newVersion = newVersionMatch[1];
    if (newVersion !== expectedNewVersion) {
      throw new Error(`Expected version ${expectedNewVersion}, got ${newVersion}`);
    }
  }

  async testEnvironmentSwitching() {
    // Switch to prod environment
    await this.runCommand('tools/release_helper/deploy.js', ['--checkout-env', 'prod']);

    // Verify environment file
    const envPath = path.join(this.projectRoot, 'app/env/Environment.gd');
    const envContent = await fs.readFile(envPath, 'utf8');
    
    if (!envContent.includes('SteamAppId  = "3391210"')) {
      throw new Error('Production environment not set correctly');
    }

    // Switch to demo environment
    await this.runCommand('tools/release_helper/deploy.js', ['--checkout-env', 'demo']);

    // Verify demo environment
    const demoEnvContent = await fs.readFile(envPath, 'utf8');
    
    if (!demoEnvContent.includes('SteamAppId  = "3612920"')) {
      throw new Error('Demo environment not set correctly');
    }
  }

  async testConfigurationLoading() {
    const ConfigManager = require('./lib/config');
    const Logger = require('./lib/logger');

    const logger = new Logger('test.log');
    const configManager = new ConfigManager('../../../deploy.config.json', logger);

    const config = await configManager.loadConfig();

    if (!config || typeof config !== 'object') {
      throw new Error(`Configuration not loaded correctly: ${JSON.stringify(config)}`);
    }

    if (!config.prod || !config.demo) {
      throw new Error(`Missing prod or demo config: ${JSON.stringify(config)}`);
    }

    if (config.prod.appId !== 3391210) {
      throw new Error(`Production app ID incorrect: expected 3391210, got ${config.prod.appId}`);
    }

    if (config.demo.appId !== 3612920) {
      throw new Error(`Demo app ID incorrect: expected 3612920, got ${config.demo.appId}`);
    }
  }

  async testBuildManagerInitialization() {
    const BuildManager = require('./lib/build');
    const Logger = require('./lib/logger');
    
    const logger = new Logger('test.log');
    const buildManager = new BuildManager(logger);
    
    const builds = await buildManager.listBuilds();
    
    if (!builds.windows || !builds.linux || !builds.mac) {
      throw new Error('Build manager not initialized correctly');
    }
  }

  async testSteamUploaderInitialization() {
    const SteamUploader = require('./lib/steam');
    const ConfigManager = require('./lib/config');
    const Logger = require('./lib/logger');

    const logger = new Logger('test.log');
    const configManager = new ConfigManager('../../../deploy.config.json', logger);
    const steamUploader = new SteamUploader(configManager, logger);

    // Ensure steam logs directory exists
    const steamLogsDir = path.join(this.projectRoot, '_release/steam_logs');
    await fs.ensureDir(steamLogsDir);

    // Test VDF generation
    const vdfPath = await steamUploader.generateVDF(
      3391210,
      { windows: 3391212, linux: 3391211, mac: 3391213 },
      'Test build'
    );

    if (!await fs.pathExists(vdfPath)) {
      throw new Error('VDF file not generated');
    }

    const vdfContent = await fs.readFile(vdfPath, 'utf8');
    if (!vdfContent.includes('3391210')) {
      throw new Error('VDF content incorrect');
    }
  }

  async testLoggerFunctionality() {
    const Logger = require('./lib/logger');
    
    const testLogFile = path.join(__dirname, 'test-logger.log');
    const logger = new Logger(testLogFile);
    
    logger.info('Test message');
    logger.success('Test success');
    logger.warn('Test warning');
    
    // Give it a moment to write
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (!await fs.pathExists(testLogFile)) {
      throw new Error('Log file not created');
    }

    const logContent = await fs.readFile(testLogFile, 'utf8');
    if (!logContent.includes('Test message')) {
      throw new Error('Log content not written correctly');
    }

    // Clean up
    await fs.remove(testLogFile);
  }

  async runAllTests() {
    console.log('🚀 Starting Deployment Tool Tests\n');

    await this.runTest('Dry run functionality', () => this.testDryRun());
    await this.runTest('Version bumping', () => this.testVersionBumping());
    await this.runTest('Environment switching', () => this.testEnvironmentSwitching());
    await this.runTest('Configuration loading', () => this.testConfigurationLoading());
    await this.runTest('Build manager initialization', () => this.testBuildManagerInitialization());
    await this.runTest('Steam uploader initialization', () => this.testSteamUploaderInitialization());
    await this.runTest('Logger functionality', () => this.testLoggerFunctionality());

    console.log('\n📊 Test Results:');
    console.log(`✅ Passed: ${this.passed}`);
    console.log(`❌ Failed: ${this.failed}`);
    console.log(`📈 Success Rate: ${Math.round((this.passed / (this.passed + this.failed)) * 100)}%`);

    if (this.failed > 0) {
      console.log('\n⚠️  Some tests failed. Please review the errors above.');
      process.exit(1);
    } else {
      console.log('\n🎉 All tests passed! Deployment tool is ready to use.');
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new DeploymentTester();
  tester.runAllTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = DeploymentTester;
