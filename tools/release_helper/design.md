# Deployment Script Design Document

## Overview

This document outlines the design for a Node.js-based deployment script (`deploy.js`) that automates the process of building and uploading the Tryü Sora Space Shooter game to Steam for both production and demo environments.

## Architecture

### Core Components

1. **Configuration Manager** - Handles loading and parsing `deploy.config.json`
2. **Version Manager** - Manages version bumping in `app/Config.gd`
3. **Environment Manager** - Handles environment file switching
4. **Build Manager** - Orchestrates Godot CLI builds for different platforms
5. **Steam Uploader** - Manages Steam uploads via SteamCMD
6. **Logger** - Provides detailed logging to `deploy.log`
7. **CLI Parser** - Handles command-line argument parsing

### File Structure

```
tools/
├── deploy.js              # Main deployment script
├── lib/
│   ├── config.js          # Configuration management
│   ├── version.js         # Version bumping logic
│   ├── environment.js     # Environment switching
│   ├── build.js           # Build orchestration
│   ├── steam.js           # Steam upload logic
│   └── logger.js          # Logging utilities
├── templates/
│   └── app_build.vdf      # Steam VDF template
├── deploy.config.json     # Deployment configuration
├── deploy.log             # Generated log file
└── requirements.md        # Requirements document
```

## Data Flow

### 1. Configuration Loading
- Load `deploy.config.json` with environment-specific app IDs and depot IDs
- Validate required configuration fields
- Set up logging to `deploy.log`

### 2. Version Management
- Parse current version from `app/Config.gd`
- Increment specified version segment (major/minor/patch)
- Update version in `app/Config.gd`
- Format version for 4-number platforms (major.minor.patch.0)

### 3. Environment Switching
- Copy appropriate environment file to `app/Environment.gd`
- Validate environment file exists
- Ensure `app/Environment.gd` is in `.gitignore`

### 4. Build Process
- Clean target directories (`_release/[os]/current/`)
- Execute Godot CLI exports for selected platforms
- Investigate and implement build version injection if supported
- Verify build outputs exist and are non-empty

### 5. Steam Upload
- Generate VDF files with correct app ID and depot IDs
- Execute SteamCMD with generated VDF
- Include content description if provided
- Report upload status for each depot

## Component Design

### Configuration Manager (`lib/config.js`)

```javascript
class ConfigManager {
  constructor(configPath = 'deploy.config.json')
  loadConfig()
  getEnvironmentConfig(env) // 'prod' or 'demo'
  validateConfig()
}
```

**Responsibilities:**
- Load and parse JSON configuration
- Validate required fields (appId, depots)
- Provide environment-specific configuration

### Version Manager (`lib/version.js`)

```javascript
class VersionManager {
  constructor(configPath = 'app/Config.gd')
  getCurrentVersion()
  bumpVersion(type) // 'major', 'minor', 'patch'
  updateConfigFile(newVersion)
  formatVersionForPlatform(version, platform)
}
```

**Responsibilities:**
- Parse SemVer from `app/Config.gd`
- Increment version segments
- Update version in configuration file
- Handle 4-number versioning for Windows/macOS

### Environment Manager (`lib/environment.js`)

```javascript
class EnvironmentManager {
  constructor()
  switchEnvironment(env) // 'prod' or 'demo'
  validateEnvironmentFile(env)
  ensureGitIgnore()
}
```

**Responsibilities:**
- Copy environment files to `app/Environment.gd`
- Validate source environment files exist
- Manage `.gitignore` entries

### Build Manager (`lib/build.js`)

```javascript
class BuildManager {
  constructor(logger)
  buildAll()
  buildPlatform(platform) // 'windows', 'linux', 'mac'
  cleanBuildDirectories(platforms)
  executeGodotExport(platform, preset)
  verifyBuildOutput(platform)
}
```

**Responsibilities:**
- Execute Godot CLI exports
- Manage build directories
- Handle platform-specific build logic
- Verify build success

### Steam Uploader (`lib/steam.js`)

```javascript
class SteamUploader {
  constructor(config, logger)
  upload(environment, contentDesc)
  generateVDF(appId, depots, contentDesc)
  executeSteamCMD(vdfPath)
  validateUpload()
}
```

**Responsibilities:**
- Generate Steam VDF files
- Execute SteamCMD uploads
- Handle upload verification
- Manage depot-specific uploads

### Logger (`lib/logger.js`)

```javascript
class Logger {
  constructor(logPath = 'deploy.log')
  info(message)
  error(message)
  warn(message)
  debug(message)
}
```

**Responsibilities:**
- Write timestamped logs to file
- Provide console output
- Support different log levels

## Error Handling Strategy

### Fail-Fast Approach
- Validate all prerequisites before starting any operations
- Check file existence and permissions early
- Validate configuration completeness

### Error Recovery
- Provide clear error messages with suggested fixes
- Log all errors with full context
- Clean up partial operations on failure

### Validation Points
1. Configuration file exists and is valid JSON
2. Required environment files exist
3. Godot CLI is available and functional
4. SteamCMD is available and configured
5. Target directories are writable
6. Version format is valid SemVer

## Platform-Specific Considerations

### Windows
- Handle .exe extensions
- Support 4-number versioning (major.minor.patch.0)
- Manage Windows-specific export settings

### Linux
- Handle executable permissions
- Support AppImage or binary formats
- Manage Linux-specific export settings

### macOS
- Handle .app bundle extraction
- Support 4-number versioning (major.minor.patch.0)
- Manage macOS-specific export settings

## Security Considerations

- Never log Steam credentials
- Validate all file paths to prevent directory traversal
- Use secure temporary file handling
- Sanitize user input for content descriptions

## Performance Considerations

- Parallel builds where possible
- Efficient file operations
- Minimal memory footprint
- Fast startup time

## Testing Strategy

- Unit tests for each component
- Integration tests for full workflows
- Mock external dependencies (Godot CLI, SteamCMD)
- Test error conditions and edge cases
