#!/bin/bash

echo "🗑️  Removing all .import files from git tracking..."

# Get all tracked .import files and remove them from git
git ls-files | grep "\.import$" | while IFS= read -r file; do
    if git rm --cached "$file" 2>/dev/null; then
        echo "✅ Removed: $file"
    else
        echo "❌ Failed: $file"
    fi
done

echo "📊 Checking remaining .import files..."
remaining=$(git ls-files | grep "\.import$" | wc -l)
echo "📁 Remaining tracked .import files: $remaining"

if [ "$remaining" -eq 0 ]; then
    echo "✅ All .import files successfully removed from git tracking!"
    echo "💡 They will now be ignored by .gitignore patterns."
else
    echo "⚠️  Some .import files may still be tracked."
fi
