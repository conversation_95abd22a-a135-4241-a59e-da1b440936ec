{"name": "tryu-deployment-tools", "version": "1.0.0", "description": "Deployment tools for Tryu Sora Space Shooter - automated versioning, building, and Steam deployment", "main": "deploy.js", "scripts": {"deploy": "node deploy.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["deployment", "godot", "steam", "build-automation"], "author": "Tryu Development Team", "license": "MIT", "dependencies": {"commander": "^11.1.0", "fs-extra": "^11.2.0"}, "engines": {"node": ">=12.0.0"}}