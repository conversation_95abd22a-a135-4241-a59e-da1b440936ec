#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');

/**
 * Comprehensive pixel art fix for Godot projects
 * Fixes all common causes of blurry sprites
 */

async function fixPixelArt() {
  console.log('🎨 Fixing pixel art settings for crisp sprites...');
  
  const projectRoot = path.resolve(__dirname, '../..');
  const appDir = path.join(projectRoot, 'app');
  const projectFile = path.join(appDir, 'project.godot');
  
  let fixedCount = 0;
  
  // 1. Fix project.godot settings
  console.log('📝 Checking project.godot settings...');
  
  if (await fs.pathExists(projectFile)) {
    let content = await fs.readFile(projectFile, 'utf8');
    let modified = false;
    
    // Fix importer defaults
    if (content.includes('"flags/filter": true')) {
      content = content.replace(/"flags\/filter": true/g, '"flags/filter": false');
      console.log('✅ Fixed importer default: flags/filter = false');
      modified = true;
      fixedCount++;
    }
    
    if (content.includes('"process/fix_alpha_border": true')) {
      content = content.replace(/"process\/fix_alpha_border": true/g, '"process/fix_alpha_border": false');
      console.log('✅ Fixed importer default: fix_alpha_border = false');
      modified = true;
      fixedCount++;
    }
    
    // Fix stretch mode for pixel art
    if (content.includes('window/stretch/mode="2d"')) {
      content = content.replace(/window\/stretch\/mode="2d"/g, 'window/stretch/mode="viewport"');
      console.log('✅ Fixed stretch mode: viewport (prevents scaling blur)');
      modified = true;
      fixedCount++;
    }
    
    // Add pixel snap if missing
    if (!content.includes('2d/use_pixel_snap=true')) {
      const renderingSection = content.indexOf('[rendering]');
      if (renderingSection !== -1) {
        const nextSection = content.indexOf('\n[', renderingSection + 1);
        const insertPos = nextSection !== -1 ? nextSection : content.length;
        const beforeInsert = content.substring(0, insertPos);
        const afterInsert = content.substring(insertPos);
        
        if (!beforeInsert.includes('2d/use_pixel_snap')) {
          content = beforeInsert + '\n2d/use_pixel_snap=true' + afterInsert;
          console.log('✅ Added pixel snap for crisp rendering');
          modified = true;
          fixedCount++;
        }
      }
    }
    
    if (modified) {
      await fs.writeFile(projectFile, content, 'utf8');
      console.log('💾 Updated project.godot');
    }
  }
  
  // 2. Fix all import files
  console.log('📁 Fixing texture import files...');
  
  const importFiles = [];
  
  function findImportFiles(dir) {
    const items = fs.readdirSync(dir);
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        findImportFiles(fullPath);
      } else if (item.endsWith('.png.import') || item.endsWith('.jpg.import')) {
        importFiles.push(fullPath);
      }
    }
  }
  
  findImportFiles(appDir);
  
  let importFixedCount = 0;
  for (const importFile of importFiles) {
    try {
      const content = await fs.readFile(importFile, 'utf8');
      
      if (content.includes('flags/filter=true') || content.includes('process/fix_alpha_border=true')) {
        const fixedContent = content
          .replace(/flags\/filter=true/g, 'flags/filter=false')
          .replace(/process\/fix_alpha_border=true/g, 'process/fix_alpha_border=false');
        
        await fs.writeFile(importFile, fixedContent, 'utf8');
        importFixedCount++;
      }
    } catch (error) {
      console.warn(`⚠️  Could not process ${importFile}: ${error.message}`);
    }
  }
  
  if (importFixedCount > 0) {
    console.log(`✅ Fixed ${importFixedCount} import files`);
    fixedCount += importFixedCount;
  }
  
  // 3. Summary
  console.log('\n📊 Summary:');
  console.log(`✅ Total fixes applied: ${fixedCount}`);
  
  if (fixedCount > 0) {
    console.log('\n🎯 Pixel art fixes complete!');
    console.log('💡 Key changes made:');
    console.log('   • Texture filtering disabled (flags/filter=false)');
    console.log('   • Alpha border processing disabled (fix_alpha_border=false)');
    console.log('   • Stretch mode set to viewport (prevents scaling blur)');
    console.log('   • Pixel snap enabled (2d/use_pixel_snap=true)');
    console.log('\n🔄 Rebuild your project to see crisp sprites!');
  } else {
    console.log('\n✨ All pixel art settings were already correct!');
  }
}

// Run the fix
if (require.main === module) {
  fixPixelArt().catch(error => {
    console.error('❌ Failed to fix pixel art settings:', error.message);
    process.exit(1);
  });
}

module.exports = { fixPixelArt };
