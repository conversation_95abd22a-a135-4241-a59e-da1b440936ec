# Deployment Script Implementation Tasks

## Phase 1: Project Setup and Core Infrastructure

### Task 1.1: Project Structure Setup
- [ ] Create `tools/lib/` directory for core modules
- [ ] Create `tools/templates/` directory for Steam VDF templates
- [ ] Initialize `package.json` with required dependencies
- [ ] Set up basic project structure and entry point

### Task 1.2: Logging System Implementation
- [ ] Create `lib/logger.js` with file and console logging
- [ ] Implement log levels (info, warn, error, debug)
- [ ] Add timestamp formatting for log entries
- [ ] Test logging to `deploy.log` file

### Task 1.3: CLI Argument Parser
- [ ] Implement command-line argument parsing in main `deploy.js`
- [ ] Support all required flags (--bump-version, --checkout-env, etc.)
- [ ] Add help text and usage information
- [ ] Validate argument combinations and required parameters

## Phase 2: Configuration and Environment Management

### Task 2.1: Configuration Manager
- [ ] Create `lib/config.js` for configuration management
- [ ] Implement JSON loading and validation for `deploy.config.json`
- [ ] Add environment-specific configuration retrieval (prod/demo)
- [ ] Validate required fields (appId, depots for each environment)

### Task 2.2: Environment File Management
- [ ] Create `lib/environment.js` for environment switching
- [ ] Implement copying environment files to `app/Environment.gd`
- [ ] Add validation for source environment files existence
- [ ] Ensure `app/Environment.gd` is added to `.gitignore`

### Task 2.3: Version Management System
- [ ] Create `lib/version.js` for version handling
- [ ] Implement SemVer parsing from `app/Config.gd`
- [ ] Add version bumping logic (major, minor, patch)
- [ ] Implement version file updating with new version
- [ ] Add 4-number version formatting for Windows/macOS

## Phase 3: Build System Implementation

### Task 3.1: Build Manager Core
- [ ] Create `lib/build.js` for build orchestration
- [ ] Implement build directory cleaning (`_release/[os]/current/`)
- [ ] Add platform detection and validation
- [ ] Create build output verification system

### Task 3.2: Godot CLI Integration
- [ ] Research Godot CLI export commands and parameters
- [ ] Implement Godot CLI execution for each platform
- [ ] Add export preset name detection/configuration
- [ ] Handle Godot CLI error conditions and output parsing

### Task 3.3: Platform-Specific Build Logic
- [ ] Implement Windows build handling (.exe, version injection)
- [ ] Implement Linux build handling (executable permissions)
- [ ] Implement macOS build handling (.app bundle extraction)
- [ ] Add platform-specific post-build processing

### Task 3.4: Build Version Injection Research
- [ ] Investigate Godot CLI version injection capabilities
- [ ] Test version parameter passing to Godot exports
- [ ] Implement version injection if supported by CLI
- [ ] Document limitations if CLI injection not available

## Phase 4: Steam Integration

### Task 4.1: Steam VDF Template System
- [ ] Create Steam VDF template in `tools/templates/app_build.vdf`
- [ ] Implement VDF generation with dynamic values
- [ ] Add support for content descriptions in VDF
- [ ] Test VDF generation with actual depot IDs

### Task 4.2: SteamCMD Integration
- [ ] Create `lib/steam.js` for Steam upload management
- [ ] Implement SteamCMD execution and parameter handling
- [ ] Add upload progress monitoring and error detection
- [ ] Implement per-depot upload status reporting

### Task 4.3: Steam Upload Workflow
- [ ] Integrate environment-specific app IDs and depot IDs
- [ ] Add content description support for releases
- [ ] Implement upload verification and success confirmation
- [ ] Add retry logic for failed uploads

## Phase 5: Integration and Testing

### Task 5.1: Main Script Integration
- [ ] Integrate all components in main `deploy.js`
- [ ] Implement workflow orchestration (version → env → build → upload)
- [ ] Add comprehensive error handling and cleanup
- [ ] Test individual flag combinations

### Task 5.2: End-to-End Testing
- [ ] Test complete deployment workflow (prod environment)
- [ ] Test complete deployment workflow (demo environment)
- [ ] Test individual platform builds (--build-windows, etc.)
- [ ] Test error conditions and recovery

### Task 5.3: Validation and Edge Cases
- [ ] Test with missing configuration files
- [ ] Test with invalid version formats
- [ ] Test with missing Godot CLI or SteamCMD
- [ ] Test with insufficient file permissions

## Phase 6: Documentation and Finalization

### Task 6.1: Usage Documentation
- [ ] Create comprehensive README for the deployment script
- [ ] Document all command-line options and examples
- [ ] Add troubleshooting guide for common issues
- [ ] Document prerequisites and setup requirements

### Task 6.2: Configuration Examples
- [ ] Create example `deploy.config.json` with actual values
- [ ] Document environment file requirements
- [ ] Add export preset configuration guidance
- [ ] Create deployment workflow examples

### Task 6.3: Final Testing and Validation
- [ ] Perform full deployment test on production environment
- [ ] Perform full deployment test on demo environment
- [ ] Validate all logging output and error messages
- [ ] Test script idempotency (safe to run multiple times)

## Dependencies and Prerequisites

### External Tools Required
- Node.js >= 12.x
- Godot Engine CLI (headless or editor)
- SteamCMD or Steamworks SDK CLI

### Node.js Dependencies
- `commander` or `yargs` for CLI parsing
- `fs-extra` for enhanced file operations
- `path` for cross-platform path handling
- `child_process` for external command execution

### Configuration Files Required
- `deploy.config.json` with prod/demo app IDs and depot IDs
- `export_presets.cfg` with configured export presets
- Environment files in `app/env/` directory

## Estimated Timeline

- **Phase 1**: 2-3 days (Project setup and infrastructure)
- **Phase 2**: 3-4 days (Configuration and environment management)
- **Phase 3**: 4-5 days (Build system implementation)
- **Phase 4**: 3-4 days (Steam integration)
- **Phase 5**: 2-3 days (Integration and testing)
- **Phase 6**: 1-2 days (Documentation and finalization)

**Total Estimated Time**: 15-21 days

## Risk Mitigation

### High-Risk Items
- Godot CLI version injection capabilities (unknown)
- SteamCMD authentication and configuration
- Platform-specific build variations

### Mitigation Strategies
- Research Godot CLI capabilities early in Phase 3
- Test SteamCMD integration with dummy uploads
- Implement comprehensive error handling and logging
- Create fallback options for unsupported features
