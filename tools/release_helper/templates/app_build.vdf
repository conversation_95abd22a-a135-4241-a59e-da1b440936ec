"appbuild"
{
	"appid" "{{APP_ID}}"
	"desc" "{{CONTENT_DESC}}"
	"buildoutput" "."
	"contentroot" ".."
	"setlive" ""
	"preview" "0"
	"local" ""

	"depots"
	{
		"{{WINDOWS_DEPOT_ID}}"
		{
			"FileMapping"
			{
				"LocalPath" "win/current/*"
				"DepotPath" "."
				"recursive" "1"
			}
		}

		"{{LINUX_DEPOT_ID}}"
		{
			"FileMapping"
			{
				"LocalPath" "linux/current/*"
				"DepotPath" "."
				"recursive" "1"
			}
		}

		"{{MAC_DEPOT_ID}}"
		{
			"FileMapping"
			{
				"LocalPath" "mac/current/*"
				"DepotPath" "."
				"recursive" "1"
			}
		}
	}
}
